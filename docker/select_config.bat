@echo off
echo ========================================
echo R2R SiliconFlow 配置选择工具
echo ========================================

echo.
echo 可用的配置选项:
echo 1. 简单配置 (推荐) - 不包含重排序功能，兼容性最好
echo 2. 标准配置 - 包含SiliconFlow嵌入，但禁用重排序
echo 3. 完整配置 - 包含本地HuggingFace重排序模型
echo.

set /p choice="请选择配置 (1-3): "

if "%choice%"=="1" (
    echo 选择简单配置...
    set config_file=r2r_siliconflow_simple.toml
    echo 这个配置提供最佳兼容性，不包含重排序功能
) else if "%choice%"=="2" (
    echo 选择标准配置...
    set config_file=r2r_siliconflow.toml
    echo 这个配置使用SiliconFlow嵌入，但禁用重排序
) else if "%choice%"=="3" (
    echo 选择完整配置...
    set config_file=r2r_siliconflow_with_rerank.toml
    echo 这个配置包含本地重排序模型，需要更多资源
) else (
    echo 无效选择，使用默认简单配置
    set config_file=r2r_siliconflow_simple.toml
)

echo.
echo 更新环境配置文件...

:: 创建临时文件
echo # R2R > temp_r2r.env
echo R2R_PORT=7272 >> temp_r2r.env
echo R2R_HOST=0.0.0.0 >> temp_r2r.env
echo R2R_LOG_LEVEL=INFO >> temp_r2r.env
echo R2R_CONFIG_NAME= >> temp_r2r.env
echo R2R_CONFIG_PATH=/app/user_configs/%config_file% >> temp_r2r.env
echo R2R_PROJECT_NAME=r2r_default >> temp_r2r.env
echo R2R_SECRET_KEY= >> temp_r2r.env
echo R2R_USER_TOOLS_PATH=/app/user_tools >> temp_r2r.env
echo R2R_LOG_FORMAT= >> temp_r2r.env

:: 添加其余配置
echo. >> temp_r2r.env
echo # Postgres Configuration >> temp_r2r.env
echo R2R_POSTGRES_USER=postgres >> temp_r2r.env
echo R2R_POSTGRES_PASSWORD=postgres >> temp_r2r.env
echo R2R_POSTGRES_HOST=postgres >> temp_r2r.env
echo R2R_POSTGRES_PORT=5432 >> temp_r2r.env
echo R2R_POSTGRES_DBNAME=postgres >> temp_r2r.env
echo R2R_POSTGRES_MAX_CONNECTIONS=1024 >> temp_r2r.env
echo R2R_POSTGRES_STATEMENT_CACHE_SIZE=100 >> temp_r2r.env

:: 复制其余内容
echo. >> temp_r2r.env
echo # Hatchet >> temp_r2r.env
echo HATCHET_CLIENT_TLS_STRATEGY=none >> temp_r2r.env
echo. >> temp_r2r.env
echo # OpenAI >> temp_r2r.env
echo OPENAI_API_KEY= >> temp_r2r.env
echo OPENAI_API_BASE= >> temp_r2r.env
echo. >> temp_r2r.env
echo # Azure Foundry >> temp_r2r.env
echo AZURE_FOUNDRY_API_ENDPOINT= >> temp_r2r.env
echo AZURE_FOUNDRY_API_KEY= >> temp_r2r.env
echo. >> temp_r2r.env
echo # XAI / GROK >> temp_r2r.env
echo XAI_API_KEY= >> temp_r2r.env
echo. >> temp_r2r.env
echo # Anthropic >> temp_r2r.env
echo ANTHROPIC_API_KEY= >> temp_r2r.env
echo. >> temp_r2r.env
echo # Azure >> temp_r2r.env
echo AZURE_API_KEY= >> temp_r2r.env
echo AZURE_API_BASE= >> temp_r2r.env
echo AZURE_API_VERSION= >> temp_r2r.env
echo. >> temp_r2r.env
echo # Google Vertex AI >> temp_r2r.env
echo GOOGLE_APPLICATION_CREDENTIALS= >> temp_r2r.env
echo VERTEX_PROJECT= >> temp_r2r.env
echo VERTEX_LOCATION= >> temp_r2r.env
echo. >> temp_r2r.env
echo # Google Gemini >> temp_r2r.env
echo GEMINI_API_KEY= >> temp_r2r.env
echo. >> temp_r2r.env
echo # Mistral >> temp_r2r.env
echo MISTRAL_API_KEY= >> temp_r2r.env
echo. >> temp_r2r.env
echo # AWS Bedrock >> temp_r2r.env
echo AWS_ACCESS_KEY_ID= >> temp_r2r.env
echo AWS_SECRET_ACCESS_KEY= >> temp_r2r.env
echo AWS_REGION_NAME= >> temp_r2r.env
echo. >> temp_r2r.env
echo # Groq >> temp_r2r.env
echo GROQ_API_KEY= >> temp_r2r.env
echo. >> temp_r2r.env
echo # Cohere >> temp_r2r.env
echo COHERE_API_KEY= >> temp_r2r.env
echo. >> temp_r2r.env
echo # Anyscale >> temp_r2r.env
echo ANYSCALE_API_KEY= >> temp_r2r.env
echo. >> temp_r2r.env
echo # Ollama >> temp_r2r.env
echo OLLAMA_API_BASE=http://host.docker.internal:11434 >> temp_r2r.env
echo. >> temp_r2r.env
echo # LM Studio >> temp_r2r.env
echo LM_STUDIO_API_BASE=http://host.docker.internal:1234 >> temp_r2r.env
echo LM_STUDIO_API_KEY=1234 >> temp_r2r.env
echo. >> temp_r2r.env
echo # Huggingface >> temp_r2r.env
echo HUGGINGFACE_API_BASE=http://host.docker.internal:8080 >> temp_r2r.env
echo HUGGINGFACE_API_KEY= >> temp_r2r.env
echo. >> temp_r2r.env
echo # Unstructured >> temp_r2r.env
echo UNSTRUCTURED_API_KEY= >> temp_r2r.env
echo UNSTRUCTURED_API_URL=https://api.unstructured.io/general/v0/general >> temp_r2r.env
echo UNSTRUCTURED_SERVICE_URL=http://unstructured:7275 >> temp_r2r.env
echo UNSTRUCTURED_NUM_WORKERS=10 >> temp_r2r.env
echo. >> temp_r2r.env
echo # Graphologic >> temp_r2r.env
echo CLUSTERING_SERVICE_URL=http://graph_clustering:7276 >> temp_r2r.env
echo. >> temp_r2r.env
echo # OAuth Credentials >> temp_r2r.env
echo GOOGLE_CLIENT_ID= >> temp_r2r.env
echo GOOGLE_CLIENT_SECRET= >> temp_r2r.env
echo GOOGLE_REDIRECT_URI= >> temp_r2r.env
echo. >> temp_r2r.env
echo GITHUB_CLIENT_ID= >> temp_r2r.env
echo GITHUB_CLIENT_SECRET= >> temp_r2r.env
echo GITHUB_REDIRECT_URI= >> temp_r2r.env
echo. >> temp_r2r.env
echo # Email >> temp_r2r.env
echo MAILERSEND_API_KEY= >> temp_r2r.env
echo SENDGRID_API_KEY= >> temp_r2r.env
echo. >> temp_r2r.env
echo # Websearch >> temp_r2r.env
echo FIRECRAWL_API_KEY= >> temp_r2r.env
echo SERPER_API_KEY= >> temp_r2r.env
echo TAVILY_API_KEY= >> temp_r2r.env
echo. >> temp_r2r.env
echo # SiliconFlow API Key (请替换为您的实际API密钥) >> temp_r2r.env
echo SILICONFLOW_API_KEY=YOUR_SILICONFLOW_API_KEY_HERE >> temp_r2r.env
echo. >> temp_r2r.env
echo # Sentry Tracing >> temp_r2r.env
echo R2R_SENTRY_DSN= >> temp_r2r.env
echo R2R_SENTRY_ENVIRONMENT= >> temp_r2r.env
echo R2R_SENTRY_TRACES_SAMPLE_RATE= >> temp_r2r.env
echo R2R_SENTRY_PROFILES_SAMPLE_RATE= >> temp_r2r.env

:: 替换原文件
move temp_r2r.env env\r2r.env

echo.
echo ========================================
echo 配置更新完成！
echo ========================================
echo.
echo 当前配置: %config_file%
echo.
echo 重要提醒:
echo 1. 请确保在 env\r2r.env 中设置您的 SILICONFLOW_API_KEY
echo 2. 现在可以运行 deploy_siliconflow.bat 来部署服务
echo.
pause
