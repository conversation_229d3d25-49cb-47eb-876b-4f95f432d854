#!/usr/bin/env python3
"""
集成脚本：将自定义SiliconFlow提供程序添加到R2R中
Integration script: Add custom SiliconFlow provider to R2R
"""

import os
import shutil
import sys
from pathlib import Path


def integrate_siliconflow_provider():
    """
    将自定义SiliconFlow提供程序集成到R2R代码库中
    """
    print("🚀 开始集成自定义SiliconFlow提供程序...")
    
    # 定义路径
    script_dir = Path(__file__).parent
    provider_source = script_dir / "siliconflow_embedding_provider.py"
    
    # R2R提供程序目录
    r2r_providers_dir = Path("../py/core/providers/embeddings")
    provider_target = r2r_providers_dir / "siliconflow.py"
    
    # 检查源文件是否存在
    if not provider_source.exists():
        print(f"❌ 错误：找不到源文件 {provider_source}")
        return False
    
    # 检查目标目录是否存在
    if not r2r_providers_dir.exists():
        print(f"❌ 错误：R2R提供程序目录不存在 {r2r_providers_dir}")
        print("请确保您在正确的R2R项目目录中运行此脚本")
        return False
    
    try:
        # 1. 复制提供程序文件
        print(f"📁 复制提供程序文件到 {provider_target}")
        shutil.copy2(provider_source, provider_target)
        
        # 2. 更新 __init__.py
        init_file = r2r_providers_dir / "__init__.py"
        print(f"📝 更新 {init_file}")
        
        with open(init_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加导入
        if "from .siliconflow import SiliconFlowEmbeddingProvider" not in content:
            # 在现有导入后添加新导入
            import_line = "from .siliconflow import SiliconFlowEmbeddingProvider\n"
            lines = content.split('\n')
            
            # 找到最后一个from导入行
            last_import_idx = -1
            for i, line in enumerate(lines):
                if line.startswith("from ."):
                    last_import_idx = i
            
            if last_import_idx >= 0:
                lines.insert(last_import_idx + 1, import_line.strip())
            else:
                lines.insert(0, import_line.strip())
            
            # 更新__all__列表
            for i, line in enumerate(lines):
                if line.strip().startswith("__all__"):
                    # 找到__all__列表的结束
                    j = i
                    while j < len(lines) and "]" not in lines[j]:
                        j += 1
                    
                    if j < len(lines):
                        # 在最后一个项目前添加新项目
                        lines[j] = lines[j].replace("]", '    "SiliconFlowEmbeddingProvider",\n]')
                    break
            
            content = '\n'.join(lines)
            
            with open(init_file, 'w', encoding='utf-8') as f:
                f.write(content)
        
        # 3. 更新工厂类
        factory_file = Path("../py/core/main/assembly/factory.py")
        print(f"🏭 更新工厂类 {factory_file}")
        
        if factory_file.exists():
            with open(factory_file, 'r', encoding='utf-8') as f:
                factory_content = f.read()
            
            # 添加SiliconFlow提供程序支持
            if "elif embedding.provider == \"siliconflow\":" not in factory_content:
                # 找到create_embedding_provider方法
                lines = factory_content.split('\n')
                for i, line in enumerate(lines):
                    if "elif embedding.provider == \"litellm\":" in line:
                        # 在litellm条件后添加siliconflow条件
                        j = i
                        while j < len(lines) and "embedding_provider = LiteLLMEmbeddingProvider(embedding)" not in lines[j]:
                            j += 1
                        
                        if j < len(lines):
                            # 添加新的条件分支
                            new_lines = [
                                "",
                                "        elif embedding.provider == \"siliconflow\":",
                                "            from core.providers import SiliconFlowEmbeddingProvider",
                                "",
                                "            embedding_provider = SiliconFlowEmbeddingProvider(embedding)",
                            ]
                            
                            for k, new_line in enumerate(new_lines):
                                lines.insert(j + 1 + k, new_line)
                            break
                
                # 更新类型注解
                for i, line in enumerate(lines):
                    if "LiteLLMEmbeddingProvider" in line and "OllamaEmbeddingProvider" in line and "OpenAIEmbeddingProvider" in line:
                        if "SiliconFlowEmbeddingProvider" not in line:
                            lines[i] = line.replace(
                                "OpenAIEmbeddingProvider",
                                "OpenAIEmbeddingProvider\n        | SiliconFlowEmbeddingProvider"
                            )
                        break
                
                factory_content = '\n'.join(lines)
                
                with open(factory_file, 'w', encoding='utf-8') as f:
                    f.write(factory_content)
        
        # 4. 更新基础配置
        base_config_file = Path("../py/core/base/providers/embedding.py")
        print(f"⚙️ 更新基础配置 {base_config_file}")
        
        if base_config_file.exists():
            with open(base_config_file, 'r', encoding='utf-8') as f:
                config_content = f.read()
            
            # 添加siliconflow到支持的提供程序列表
            if "\"siliconflow\"" not in config_content:
                config_content = config_content.replace(
                    'return ["litellm", "openai", "ollama"]',
                    'return ["litellm", "openai", "ollama", "siliconflow"]'
                )
                
                with open(base_config_file, 'w', encoding='utf-8') as f:
                    f.write(config_content)
        
        print("✅ 自定义SiliconFlow提供程序集成完成！")
        print("\n📋 下一步操作：")
        print("1. 重新构建Docker镜像")
        print("2. 使用 r2r_siliconflow_with_custom_rerank.toml 配置文件")
        print("3. 确保设置了 SILICONFLOW_API_KEY 环境变量")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成过程中出现错误：{str(e)}")
        return False


def create_deployment_script():
    """
    创建使用自定义重排序的部署脚本
    """
    script_content = '''@echo off
echo ========================================
echo R2R SiliconFlow with Custom Reranking
echo ========================================

echo.
echo This deployment uses custom SiliconFlow provider with Qwen3-Reranker-8B support
echo.

echo Checking configuration files...
if not exist "user_configs\\r2r_siliconflow_with_custom_rerank.toml" (
    echo ERROR: Configuration file user_configs\\r2r_siliconflow_with_custom_rerank.toml not found
    pause
    exit /b 1
)

echo.
echo IMPORTANT: Make sure you have set SILICONFLOW_API_KEY in env\\r2r.env
echo.

echo Updating configuration path...
powershell -Command "(Get-Content env\\r2r.env) -replace 'R2R_CONFIG_PATH=.*', 'R2R_CONFIG_PATH=/app/user_configs/r2r_siliconflow_with_custom_rerank.toml' | Set-Content env\\r2r.env"

echo.
echo Stopping existing containers...
docker-compose down

echo.
echo Rebuilding R2R image with custom provider...
docker-compose build r2r

echo.
echo Starting services in order...
echo 1. Starting database service...
docker-compose --profile postgres up -d postgres

echo Waiting for database to start...
timeout /t 15 /nobreak > nul

echo 2. Starting MinIO storage service...
docker-compose --profile minio up -d minio

echo Waiting for MinIO to start...
timeout /t 10 /nobreak > nul

echo 3. Starting graph clustering service...
docker-compose up -d graph_clustering

echo Waiting for graph clustering service to start...
timeout /t 10 /nobreak > nul

echo 4. Starting R2R core service with custom provider...
docker-compose up -d r2r

echo Waiting for R2R service to start...
timeout /t 20 /nobreak > nul

echo 5. Starting R2R Dashboard...
docker-compose up -d r2r-dashboard

echo.
echo ========================================
echo Deployment with Custom Reranking Complete!
echo ========================================
echo.
echo Service URLs:
echo - R2R API: http://localhost:7272
echo - R2R Dashboard: http://localhost:7273
echo.
echo Features enabled:
echo - Qwen3-Embedding-8B (1024 dimensions)
echo - Qwen3-Reranker-8B (custom SiliconFlow reranking)
echo.
echo Checking service status...
docker-compose ps

echo.
echo If you encounter issues, check logs with:
echo docker-compose logs r2r
echo.
pause
'''
    
    with open("deploy_with_custom_reranking.bat", 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("📝 创建了部署脚本：deploy_with_custom_reranking.bat")


if __name__ == "__main__":
    print("🔧 SiliconFlow自定义重排序集成工具")
    print("=" * 50)
    
    choice = input("选择操作：\n1. 集成自定义提供程序到R2R\n2. 创建部署脚本\n3. 全部执行\n请输入选择 (1-3): ")
    
    if choice == "1":
        integrate_siliconflow_provider()
    elif choice == "2":
        create_deployment_script()
    elif choice == "3":
        if integrate_siliconflow_provider():
            create_deployment_script()
    else:
        print("无效选择")
        sys.exit(1)
