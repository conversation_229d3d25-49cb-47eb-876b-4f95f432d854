"""
Custom SiliconFlow Embedding Provider for R2R
Supports both embedding and reranking via SiliconFlow API
"""

import contextlib
import logging
import math
import os
from copy import copy
from typing import Any

import litellm
import requests
from aiohttp import ClientError, ClientSession
from litellm import AuthenticationError, aembedding, embedding

from core.base import (
    ChunkSearchResult,
    EmbeddingConfig,
    EmbeddingProvider,
    R2RException,
)

logger = logging.getLogger()


class SiliconFlowEmbeddingProvider(EmbeddingProvider):
    """
    Custom embedding provider that supports SiliconFlow API for both
    embeddings and reranking, extending LiteLLM functionality.
    """

    def __init__(
        self,
        config: EmbeddingConfig,
        *args,
        **kwargs,
    ) -> None:
        super().__init__(config)

        self.litellm_embedding = embedding
        self.litellm_aembedding = aembedding

        provider = config.provider
        if not provider:
            raise ValueError(
                "Must set provider in order to initialize `SiliconFlowEmbeddingProvider`."
            )
        if provider != "siliconflow":
            raise ValueError(
                "SiliconFlowEmbeddingProvider must be initialized with provider `siliconflow`."
            )

        # SiliconFlow API configuration
        self.api_base = "https://api.siliconflow.cn/v1/"
        self.api_key = os.getenv("SILICONFLOW_API_KEY")
        if not self.api_key:
            raise ValueError(
                "SILICONFLOW_API_KEY environment variable must be set"
            )

        # Configure reranking
        self.rerank_model = config.rerank_model
        self.supports_reranking = bool(self.rerank_model)

        self.base_model = config.base_model
        self.base_dimension = config.base_dimension

        logger.info(f"Initialized SiliconFlowEmbeddingProvider with model: {self.base_model}")
        if self.supports_reranking:
            logger.info(f"Reranking enabled with model: {self.rerank_model}")

    def _get_embedding_kwargs(self, **kwargs):
        embedding_kwargs = {
            "model": self.base_model,
            "dimensions": self.base_dimension,
            "api_base": self.api_base,
            "api_key": self.api_key,
        }
        embedding_kwargs.update(kwargs)
        return embedding_kwargs

    async def _execute_task(self, task: dict[str, Any]) -> list[list[float]]:
        texts = task["texts"]
        kwargs = self._get_embedding_kwargs(**task.get("kwargs", {}))

        if "dimensions" in kwargs and math.isnan(kwargs["dimensions"]):
            kwargs.pop("dimensions")
            logger.warning("Dropping nan dimensions from kwargs")

        try:
            response = await self.litellm_aembedding(
                input=texts,
                **kwargs,
            )
            return [data["embedding"] for data in response.data]
        except AuthenticationError:
            logger.error("Authentication error: Invalid SiliconFlow API key.")
            raise
        except Exception as e:
            error_msg = f"Error getting embeddings: {str(e)}"
            logger.error(error_msg)
            raise R2RException(error_msg, 400) from e

    def _execute_task_sync(self, task: dict[str, Any]) -> list[list[float]]:
        texts = task["texts"]
        kwargs = self._get_embedding_kwargs(**task.get("kwargs", {}))
        try:
            response = self.litellm_embedding(
                input=texts,
                **kwargs,
            )
            return [data["embedding"] for data in response.data]
        except AuthenticationError:
            logger.error("Authentication error: Invalid SiliconFlow API key.")
            raise
        except Exception as e:
            error_msg = f"Error getting embeddings: {str(e)}"
            logger.error(error_msg)
            raise R2RException(error_msg, 400) from e

    async def async_get_embedding(
        self,
        text: str,
        stage: EmbeddingProvider.Step = EmbeddingProvider.Step.BASE,
        **kwargs,
    ) -> list[float]:
        if stage != EmbeddingProvider.Step.BASE:
            raise ValueError(
                "SiliconFlowEmbeddingProvider only supports search stage."
            )

        task = {
            "texts": [text],
            "stage": stage,
            "kwargs": kwargs,
        }
        return (await self._execute_with_backoff_async(task))[0]

    def get_embedding(
        self,
        text: str,
        stage: EmbeddingProvider.Step = EmbeddingProvider.Step.BASE,
        **kwargs,
    ) -> list[float]:
        if stage != EmbeddingProvider.Step.BASE:
            raise ValueError(
                "SiliconFlowEmbeddingProvider only supports search stage."
            )

        task = {
            "texts": [text],
            "stage": stage,
            "kwargs": kwargs,
        }
        return self._execute_with_backoff_sync(task)[0]

    async def async_get_embeddings(
        self,
        texts: list[str],
        stage: EmbeddingProvider.Step = EmbeddingProvider.Step.BASE,
        **kwargs,
    ) -> list[list[float]]:
        if stage != EmbeddingProvider.Step.BASE:
            raise ValueError(
                "SiliconFlowEmbeddingProvider only supports search stage."
            )

        task = {
            "texts": texts,
            "stage": stage,
            "kwargs": kwargs,
        }
        return await self._execute_with_backoff_async(task)

    def get_embeddings(
        self,
        texts: list[str],
        stage: EmbeddingProvider.Step = EmbeddingProvider.Step.BASE,
        **kwargs,
    ) -> list[list[float]]:
        if stage != EmbeddingProvider.Step.BASE:
            raise ValueError(
                "SiliconFlowEmbeddingProvider only supports search stage."
            )

        task = {
            "texts": texts,
            "stage": stage,
            "kwargs": kwargs,
        }
        return self._execute_with_backoff_sync(task)

    def _call_siliconflow_rerank_api(self, query: str, texts: list[str]) -> list[dict]:
        """
        Call SiliconFlow reranking API with the correct format
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # SiliconFlow reranking API format
        payload = {
            "model": self.rerank_model,
            "query": query,
            "documents": texts,
            "top_k": len(texts),  # Return all results with scores
            "return_documents": False  # We only need scores and indices
        }

        try:
            response = requests.post(
                f"{self.api_base}rerank",
                json=payload,
                headers=headers,
                timeout=30
            )
            response.raise_for_status()
            return response.json()["results"]
        except requests.RequestException as e:
            logger.error(f"SiliconFlow reranking API error: {str(e)}")
            raise

    async def _call_siliconflow_rerank_api_async(self, query: str, texts: list[str]) -> list[dict]:
        """
        Async version of SiliconFlow reranking API call
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.rerank_model,
            "query": query,
            "documents": texts,
            "top_k": len(texts),
            "return_documents": False
        }

        try:
            async with ClientSession() as session:
                async with session.post(
                    f"{self.api_base}rerank",
                    json=payload,
                    headers=headers,
                    timeout=30
                ) as response:
                    response.raise_for_status()
                    result = await response.json()
                    return result["results"]
        except (ClientError, Exception) as e:
            logger.error(f"SiliconFlow async reranking API error: {str(e)}")
            raise

    def rerank(
        self,
        query: str,
        results: list[ChunkSearchResult],
        stage: EmbeddingProvider.Step = EmbeddingProvider.Step.RERANK,
        limit: int = 10,
    ):
        if not self.supports_reranking:
            return results[:limit]

        texts = [result.text for result in results]

        try:
            rerank_results = self._call_siliconflow_rerank_api(query, texts)
            
            # Process results and apply scores
            scored_results = []
            for rank_info in rerank_results:
                original_result = results[rank_info["index"]]
                copied_result = copy(original_result)
                copied_result.score = rank_info["relevance_score"]
                scored_results.append(copied_result)

            # Sort by score (descending) and return limited results
            scored_results.sort(key=lambda x: x.score, reverse=True)
            return scored_results[:limit]

        except Exception as e:
            logger.error(f"Error during SiliconFlow reranking: {str(e)}")
            # Fall back to returning the original results if reranking fails
            return results[:limit]

    async def arerank(
        self,
        query: str,
        results: list[ChunkSearchResult],
        stage: EmbeddingProvider.Step = EmbeddingProvider.Step.RERANK,
        limit: int = 10,
    ) -> list[ChunkSearchResult]:
        if not self.supports_reranking:
            return results[:limit]

        texts = [result.text for result in results]

        try:
            rerank_results = await self._call_siliconflow_rerank_api_async(query, texts)
            
            # Process results and apply scores
            scored_results = []
            for rank_info in rerank_results:
                original_result = results[rank_info["index"]]
                copied_result = copy(original_result)
                copied_result.score = rank_info["relevance_score"]
                scored_results.append(copied_result)

            # Sort by score (descending) and return limited results
            scored_results.sort(key=lambda x: x.score, reverse=True)
            return scored_results[:limit]

        except Exception as e:
            logger.error(f"Error during async SiliconFlow reranking: {str(e)}")
            # Fall back to returning the original results if reranking fails
            return results[:limit]
