# 简化的SiliconFlow部署配置
# 避免一些可能有问题的组件，专注于核心功能

services:
  postgres:
    image: pgvector/pgvector:pg16
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-r2r}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  r2r:
    image: ragtoriches/r2r:main
    environment:
      # SiliconFlow配置
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_API_BASE=${OPENAI_API_BASE}
      
      # 数据库配置
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DBNAME=${POSTGRES_DB:-r2r}
      
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      
      # R2R配置
      - R2R_CONFIG_NAME=${R2R_CONFIG_NAME:-default}
      - R2R_PROJECT_NAME=r2r_default
    ports:
      - "7272:7272"
      - "7273:7273"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ../py/r2r:/app/r2r
      - ../py/core/configs:/app/core/configs
      - r2r_storage:/app/storage
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7272/v3/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data:
  r2r_storage:
