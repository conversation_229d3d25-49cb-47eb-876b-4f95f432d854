# SiliconFlow Qwen模型服务部署指南

本文档说明如何配置R2R使用SiliconFlow提供的Qwen模型服务。

## 配置信息

- **API Key**: `sk-hconcoielayrcqyfoqibziztdnchplrhkmupsvddcfkwehrd`
- **API Base URL**: `https://api.siliconflow.cn/v1/`
- **LLM模型**: `Qwen/Qwen3-32B`
- **Embedding模型**: `Qwen/Qwen3-Embedding-8B`
- **Rerank模型**: `Qwen/Qwen3-Reranker-8B`

## 已修改的配置文件

### 1. 主配置文件 (`py/r2r/r2r.toml`)

✅ **已修改的配置项**:
- 所有LLM模型设置为 `Qwen/Qwen3-32B`
- completion配置中的 `api_base` 设置为 SiliconFlow API端点
- embedding模型设置为 `Qwen/Qwen3-Embedding-8B`
- rerank模型设置为 `Qwen/Qwen3-Reranker-8B`
- embedding向量维度调整为 1024

### 2. 环境变量文件 (`docker/env/r2r-full.env`)

✅ **已设置的环境变量**:
```bash
OPENAI_API_KEY=sk-hconcoielayrcqyfoqibziztdnchplrhkmupsvddcfkwehrd
OPENAI_API_BASE=https://api.siliconflow.cn/v1/
```

### 3. 专用配置文件 (`py/core/configs/siliconflow_qwen.toml`)

✅ **创建了专用配置文件**，包含完整的SiliconFlow优化配置。

## 部署步骤

### 方法1: 使用默认配置部署

```bash
# 1. 进入docker目录
cd docker

# 2. 启动服务
docker compose -f compose.full.yaml --profile postgres up -d
```

### 方法2: 使用专用配置部署（推荐）

```bash
# 1. 设置配置名称
export R2R_CONFIG_NAME=siliconflow_qwen

# 2. 进入docker目录
cd docker

# 3. 启动服务
docker compose -f compose.full.yaml --profile postgres up -d
```

## 配置验证

### 1. 检查服务状态
```bash
docker compose -f compose.full.yaml ps
```

### 2. 查看日志
```bash
docker compose -f compose.full.yaml logs r2r
```

### 3. 测试API连接
```bash
curl -X POST "http://localhost:7272/v3/retrieval/completion" \
  -H "Content-Type: application/json" \
  -d '{
    "message": {"role": "user", "content": "Hello, test SiliconFlow connection"},
    "rag_generation_config": {"model": "Qwen/Qwen3-32B"}
  }'
```

## 重要注意事项

### 🔐 **安全性**
- API密钥已配置在环境变量中，避免在代码中硬编码
- 生产环境中请使用更安全的密钥管理方案

### ⚡ **性能优化**
- 降低了并发请求限制以适应SiliconFlow的API限制
- 调整了批次大小以优化embedding处理性能
- 增加了请求超时时间以适应大模型响应时间

### 🔧 **配置说明**
- **LLM配置**: 通过TOML文件中的 `api_base` 参数指定
- **Embedding配置**: 通过环境变量 `OPENAI_API_BASE` 指定
- **模型名称**: 使用SiliconFlow的完整模型名称格式

### 📊 **模型规格**
- **Qwen3-32B**: 320亿参数的大语言模型
- **Qwen3-Embedding-8B**: 1024维向量的embedding模型
- **Qwen3-Reranker-8B**: 高性能的重排序模型

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查环境变量中的API密钥是否正确
   - 确认SiliconFlow账户余额充足

2. **连接超时**
   - 检查网络连接
   - 增加 `request_timeout` 配置值

3. **模型不可用**
   - 确认SiliconFlow支持指定的模型
   - 检查模型名称格式是否正确

4. **向量维度错误**
   - 确认embedding模型的向量维度设置正确
   - Qwen3-Embedding-8B使用1024维向量

## 技术支持

如有问题，请检查：
1. R2R官方文档
2. SiliconFlow API文档
3. 容器日志输出
