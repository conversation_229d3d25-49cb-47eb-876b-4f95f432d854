name: 'Setup PostgreSQL'
description: 'Sets up PostgreSQL with pgvector'
inputs:
  os:
    description: 'Operating system'
    required: true
runs:
  using: "composite"
  steps:
    - name: Setup PostgreSQL on Ubuntu
      if: inputs.os == 'ubuntu-latest'
      shell: bash
      run: |
        sudo apt-get purge -y 'postgresql-*'
        sudo rm -rf /var/lib/postgresql /var/log/postgresql /etc/postgresql

        echo "deb [signed-by=/usr/share/keyrings/postgresql-archive-keyring.gpg] http://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" | sudo tee /etc/apt/sources.list.d/pgdg.list
        wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo gpg --dearmor -o /usr/share/keyrings/postgresql-archive-keyring.gpg

        sudo apt-get update
        sudo apt-get install -y postgresql-15 postgresql-client-15 postgresql-15-pgvector

        sudo systemctl enable postgresql@15-main
        sudo systemctl start postgresql@15-main
        cd /
        sudo -u postgres /usr/lib/postgresql/15/bin/psql -c "ALTER USER postgres PASSWORD 'postgres';"
        sudo -u postgres /usr/lib/postgresql/15/bin/psql -c "CREATE EXTENSION vector;"

        # Set max_connections to 1024
        echo "max_connections = 1024" | sudo tee -a /etc/postgresql/15/main/postgresql.conf
        sudo systemctl reload postgresql@15-main

    - name: Setup PostgreSQL on Windows
      if: inputs.os == 'windows-latest'
      shell: cmd
      run: |

        echo Starting PostgreSQL setup and pgvector installation...

        echo Installing PostgreSQL...
        choco install postgresql15 --params "/Password:postgres" --force

        echo Updating PATH and setting PGPASSWORD...
        set PATH=%PATH%;C:\Program Files\PostgreSQL\15\bin
        set PGPASSWORD=postgres
        echo PATH updated and PGPASSWORD set.

        echo Altering PostgreSQL user password...
        psql -U postgres -c "ALTER USER postgres PASSWORD 'postgres';"
        echo PostgreSQL user password altered.

        echo Installing Visual Studio Build Tools...
        choco install visualstudio2022buildtools --package-parameters "--add Microsoft.VisualStudio.Workload.VCTools --includeRecommended --passive --norestart"
        echo Visual Studio Build Tools installed.

        echo Setting up Visual Studio environment...
        call "C:\Program Files\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat"
        echo Visual Studio environment set up.

        echo Cloning and building pgvector...
        set PGROOT=C:\Program Files\PostgreSQL\15
        cd /d %TEMP%
        git clone --branch v0.7.4 https://github.com/pgvector/pgvector.git
        cd pgvector
        echo pgvector cloned.

        echo Creating vector extension...
        psql -U postgres -c "CREATE EXTENSION vector;"
        echo Vector extension created.

        echo Building pgvector...
        nmake /F Makefile.win
        echo pgvector built.

        echo Installing pgvector...
        nmake /F Makefile.win install
        echo pgvector installed.

        echo Setting max_connections to 1024...
        echo max_connections = 1024 >> "C:\Program Files\PostgreSQL\15\data\postgresql.conf"
        echo max_connections set.

        echo Restarting PostgreSQL service...
        net stop postgresql-x64-15
        net start postgresql-x64-15
        echo PostgreSQL service restarted.

        echo Setup complete!

    - name: Setup PostgreSQL on macOS
      if: inputs.os == 'macos-latest'
      shell: bash
      run: |
        brew update
        brew install postgresql@15

        brew services start postgresql@15
        sleep 5
        /opt/homebrew/opt/postgresql@15/bin/createuser -s postgres
        /opt/homebrew/opt/postgresql@15/bin/psql -d postgres -c "ALTER USER postgres PASSWORD 'postgres';"

        cd /tmp
        git clone --branch v0.7.4 https://github.com/pgvector/pgvector.git
        cd pgvector
        export PG_CONFIG=/opt/homebrew/opt/postgresql@15/bin/pg_config
        make
        make install # may need sudo

        # Set max_connections to 1024
        echo "max_connections = 1024" | sudo tee -a /opt/homebrew/var/postgresql@15/postgresql.conf
        brew services restart postgresql@15
