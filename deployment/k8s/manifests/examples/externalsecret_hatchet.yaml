---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: hatchet-shared-config
  annotations:
    argocd.argoproj.io/sync-wave: "-2"
spec:
  ## kubectl -n kube-system annotate es vsphere-cpi-creds force-sync=$(date +%s) --overwrite
  refreshInterval: "0"
  secretStoreRef:
    # This name must match the metadata.name in the `SecretStore`
    name: bitwarden-secretsmanager
    kind: SecretStore
    #kind: ClusterSecretStore
  target:
    name: hatchet-shared-config
    # this is how the Kind=Secret will look like
    template:
      engineVersion: v2
      data:

        ADMIN_EMAIL: "{{ .RABBITMQ_ADMIN_EMAIL }}"
        ADMIN_PASSWORD: "{{ .RABBITMQ_ADMIN_PASSWORD }}"
        DATABASE_POSTGRES_DB_NAME: "hatchet"
        DATABASE_POSTGRES_HOST: "hatchet-documentdb"
        DATABASE_POSTGRES_PASSWORD: "{{ .HATCHET_DATABASE_POSTGRES_PASSWORD }}"
        DATABASE_POSTGRES_PORT: "5432"
        DATABASE_POSTGRES_SSL_MODE: "disable"
        DATABASE_POSTGRES_USERNAME: "{{ .HATCHET_DATABASE_POSTGRES_USERNAME }}"
        DATABASE_URL: "postgres://{{ .HATCHET_DATABASE_POSTGRES_USERNAME }}:{{ .HATCHET_DATABASE_POSTGRES_PASSWORD }}@hatchet-documentdb:5432/hatchet?sslmode=disable"
        SERVER_AUTH_BASIC_AUTH_ENABLED: "t"
        SERVER_AUTH_COOKIE_DOMAIN: "localhost:8080"
        SERVER_AUTH_COOKIE_INSECURE: "t"
        SERVER_AUTH_SET_EMAIL_VERIFIED: "t"
        SERVER_GRPC_BIND_ADDRESS: "0.0.0.0"
        SERVER_GRPC_BROADCAST_ADDRESS: "controllers:7070"
        SERVER_GRPC_INSECURE: "true"
        SERVER_TASKQUEUE_RABBITMQ_URL: "amqp://{{ .RABBITMQ_DEFAULT_USER }}:{{ .RABBITMQ_DEFAULT_PASS }}@hatchet-rabbitmq:5672/"
        SERVER_URL: "http://localhost:8080"


  data:
  - secretKey: RABBITMQ_DEFAULT_PASS
    remoteRef:
      key: "6203f8e5-d273-0000-0000-aaa000000000"
  - secretKey: RABBITMQ_DEFAULT_USER
    remoteRef:
      key: "330e6465-4568-0000-0000-aaa000000000"
  - secretKey: HATCHET_DATABASE_POSTGRES_USERNAME
    remoteRef:
      key: "261e8389-852e-0000-0000-aaa000000000"
  - secretKey: HATCHET_DATABASE_POSTGRES_PASSWORD
    remoteRef:
      key: "5eb84a48-e16b-0000-0000-aaa000000000"
  - secretKey: RABBITMQ_ADMIN_EMAIL
    remoteRef:
      key: "3da5e88c-1640-0000-0000-aaa000000000"
  - secretKey: RABBITMQ_ADMIN_PASSWORD
    remoteRef:
      key: "98b55ce2-fce8-0000-0000-aaa000000000"
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: hatchet-rabbitmq-config
  annotations:
    argocd.argoproj.io/sync-wave: "-2"
spec:
  ## kubectl -n kube-system annotate es vsphere-cpi-creds force-sync=$(date +%s) --overwrite
  refreshInterval: "0"
  secretStoreRef:
    # This name must match the metadata.name in the `SecretStore`
    name: bitwarden-secretsmanager
    kind: SecretStore
    #kind: ClusterSecretStore
  target:
    name: hatchet-rabbitmq-config
    # this is how the Kind=Secret will look like
    template:
      engineVersion: v2
      data:
        rabbitmq.conf: |
          ## Username and password
          default_user = {{ .RABBITMQ_DEFAULT_USER }}
          ## Clustering
          ##
          cluster_name = hatchet-rabbitmq
          cluster_formation.peer_discovery_backend  = rabbit_peer_discovery_k8s
          cluster_formation.k8s.host = kubernetes.default
          cluster_formation.k8s.address_type = hostname
          cluster_formation.k8s.service_name = hatchet-rabbitmq-headless
          cluster_formation.k8s.hostname_suffix = .hatchet-rabbitmq-headless.ai-system.svc.cluster.local
          cluster_formation.node_cleanup.interval = 10
          cluster_formation.node_cleanup.only_log_warning = true
          cluster_partition_handling = autoheal

          # queue master locator
          queue_master_locator = min-masters
          # enable loopback user
          loopback_users.hatchet = false
          #default_vhost = ai-system-vhost
          #disk_free_limit.absolute = 50MB

  data:
  - secretKey: RABBITMQ_DEFAULT_USER
    remoteRef:
      key: "330e6465-4568-48e1-ae07-b27c001f5f08"
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: hatchet-rabbitmq
  annotations:
    argocd.argoproj.io/sync-wave: "-2"
spec:
  ## kubectl -n kube-system annotate es vsphere-cpi-creds force-sync=$(date +%s) --overwrite
  refreshInterval: "0"
  secretStoreRef:
    # This name must match the metadata.name in the `SecretStore`
    name: bitwarden-secretsmanager
    kind: SecretStore
    #kind: ClusterSecretStore
  target:
    name: hatchet-rabbitmq
    # this is how the Kind=Secret will look like
    template:
      engineVersion: v2
      data:
        rabbitmq-erlang-cookie: "{{ .rabbitmq_erlang_cookie }}"
        rabbitmq-password: "{{ .RABBITMQ_DEFAULT_PASS }}"
        rabbitmq-user: "{{ .RABBITMQ_DEFAULT_USER }}"

  data:
  - secretKey: rabbitmq_erlang_cookie
    remoteRef:
      key: "2aae42a4-8813-0000-0000-aaa000000000"
  - secretKey: RABBITMQ_DEFAULT_PASS
    remoteRef:
      key: "6203f8e5-d273-0000-0000-aaa000000000"
  - secretKey: RABBITMQ_DEFAULT_USER
    remoteRef:
      key: "330e6465-4568-0000-0000-aaa000000000"
