---
apiVersion: v1
kind: Service
metadata:
  name: hatchet-engine
spec:
  selector:
    app: hatchet-engine
  ports:
    - port: 7077
      targetPort: 7077
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: hatchet-engine
  annotations:
    argocd.argoproj.io/sync-wave: "30"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: hatchet-engine
  template:
    metadata:
      labels:
        app: hatchet-engine
    spec:
      initContainers:
      - name: wait-for-config-files
        image: busybox:1.37.0
        command:
          - /bin/sh
          - -c
          - |
            # Wait for config files to be generated by hatchet-init-job and pushed into Secret and be not empty.
            sh /init/check-file.sh /hatchet/config/server.yaml
            sh /init/check-file.sh /hatchet/config/database.yaml
            echo "Config files are ready."
        volumeMounts:
        - mountPath: /init
          name: init-scripts
        - name: config-volume
          mountPath: /hatchet/config
      containers:
      - name: hatchet-engine
        image: ghcr.io/hatchet-dev/hatchet/hatchet-engine:v0.54.4
        command: ["/hatchet/hatchet-engine", "--config", "/hatchet/config"]
        ports:
          - containerPort: 7077
        envFrom:
          - secretRef:
              name: hatchet-secrets
          - configMapRef:
              name: hatchet-configmap
        livenessProbe:
          exec:
            command: ["wget", "-q", "-O", "-", "http://localhost:8733/live"]
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 5
        readinessProbe:
          exec:
            command: ["wget", "-q", "-O", "-", "http://localhost:8733/live"]
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
          - name: certs-volume
            mountPath: /hatchet/certs
          - name: config-volume
            mountPath: /hatchet/config
      volumes:
      - configMap:
          defaultMode: 493
          name: hatchet-init-scripts
        name: init-scripts
      - name: certs-volume
        secret:
          secretName: r2r-hatchet-gen-cert-files
      - name: config-volume
        secret:
          secretName: r2r-hatchet-gen-conf-files
