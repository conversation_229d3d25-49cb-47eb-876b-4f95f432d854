# R2R SiliconFlow 自定义重排序指南

## 🎯 概述

本指南将帮助您在R2R中启用高质量的Qwen3-Reranker-8B重排序功能，通过自定义SiliconFlow提供程序实现。

## ✨ 功能特性

- **高质量嵌入**: Qwen3-Embedding-8B (1024维度)
- **先进重排序**: Qwen3-Reranker-8B 
- **完整集成**: 与R2R无缝集成
- **API兼容**: 支持SiliconFlow API格式
- **性能优化**: 异步处理和错误恢复

## 📋 前提条件

1. **SiliconFlow API密钥**: 需要有效的SiliconFlow API密钥
2. **Docker环境**: 确保Docker和Docker Compose已安装
3. **R2R项目**: 完整的R2R项目代码库

## 🚀 快速开始

### 方法1: 一键部署 (推荐)

```cmd
# 1. 设置API密钥
# 编辑 env\r2r.env 文件，设置您的SiliconFlow API密钥
SILICONFLOW_API_KEY=您的实际API密钥

# 2. 运行一键部署脚本
deploy_with_custom_reranking.bat
```

### 方法2: 分步部署

```cmd
# 1. 设置自定义提供程序
setup_custom_reranking.bat

# 2. 构建自定义镜像
docker build -f Dockerfile.custom -t r2r-custom:latest .

# 3. 启动服务
docker-compose -f docker-compose.custom.yml up -d
```

## 📁 文件说明

### 核心文件

- `siliconflow_embedding_provider.py` - 自定义SiliconFlow提供程序
- `r2r_siliconflow_with_custom_rerank.toml` - 支持重排序的配置文件
- `deploy_with_custom_reranking.bat` - 一键部署脚本

### 辅助文件

- `setup_custom_reranking.bat` - 设置脚本
- `integrate_siliconflow_provider.py` - Python集成脚本
- `CUSTOM_RERANKING_GUIDE_CN.md` - 本使用指南

## ⚙️ 配置详解

### 嵌入配置

```toml
[embedding]
provider = "siliconflow"
base_model = "Qwen/Qwen3-Embedding-8B"
base_dimension = 1024
rerank_model = "Qwen/Qwen3-Reranker-8B"
batch_size = 128
concurrent_request_limit = 256
```

### 环境变量

```env
# 必需
SILICONFLOW_API_KEY=您的API密钥

# 配置文件路径
R2R_CONFIG_PATH=/app/user_configs/r2r_siliconflow_with_custom_rerank.toml
```

## 🔧 技术实现

### 自定义提供程序特性

1. **嵌入功能**
   - 支持SiliconFlow嵌入API
   - 1024维度向量
   - 批量处理优化

2. **重排序功能**
   - SiliconFlow重排序API集成
   - 异步和同步支持
   - 错误恢复机制

3. **API兼容性**
   - 正确的SiliconFlow API格式
   - 认证头处理
   - 响应格式解析

### 集成方式

```python
# 自动集成到R2R工厂类
elif embedding.provider == "siliconflow":
    from core.providers import SiliconFlowEmbeddingProvider
    embedding_provider = SiliconFlowEmbeddingProvider(embedding)
```

## 🧪 测试验证

### 1. 健康检查

```cmd
curl http://localhost:7272/v3/health
```

### 2. 嵌入测试

```python
# 通过R2R API测试嵌入
import requests

response = requests.post(
    "http://localhost:7272/v3/embeddings",
    json={"text": "测试文本"}
)
print(response.json())
```

### 3. 重排序测试

上传文档并进行搜索，观察重排序效果：

```python
# 搜索并观察重排序结果
response = requests.post(
    "http://localhost:7272/v3/search",
    json={
        "query": "搜索查询",
        "limit": 10
    }
)
```

## 🔍 故障排除

### 常见问题

1. **API密钥错误**
   ```
   错误: Authentication error: Invalid SiliconFlow API key
   解决: 检查env\r2r.env中的SILICONFLOW_API_KEY设置
   ```

2. **镜像构建失败**
   ```
   错误: 镜像构建失败
   解决: 确保siliconflow_embedding_provider.py文件存在
   ```

3. **重排序不工作**
   ```
   错误: 重排序API调用失败
   解决: 检查SiliconFlow API配额和网络连接
   ```

### 日志查看

```cmd
# 查看R2R服务日志
docker-compose logs r2r

# 查看特定错误
docker-compose logs r2r | findstr "ERROR"

# 实时日志
docker-compose logs -f r2r
```

## 📊 性能对比

| 功能 | 简单配置 | 自定义重排序配置 |
|------|----------|------------------|
| 嵌入模型 | Qwen3-Embedding-8B | Qwen3-Embedding-8B |
| 向量维度 | 1024 | 1024 |
| 重排序 | ❌ 无 | ✅ Qwen3-Reranker-8B |
| 搜索质量 | 良好 | 优秀 |
| 响应时间 | 快 | 稍慢 (包含重排序) |

## 🔄 升级和维护

### 更新自定义提供程序

1. 修改 `siliconflow_embedding_provider.py`
2. 重新构建镜像: `docker build -f Dockerfile.custom -t r2r-custom:latest .`
3. 重启服务: `docker-compose restart r2r`

### 回退到简单配置

```cmd
# 更新配置文件路径
powershell -Command "(Get-Content env\r2r.env) -replace 'R2R_CONFIG_PATH=.*', 'R2R_CONFIG_PATH=/app/user_configs/r2r_siliconflow_simple.toml' | Set-Content env\r2r.env"

# 使用原始镜像重启
docker-compose down
docker-compose up -d
```

## 🎉 成功部署验证

部署成功后，您应该看到：

1. ✅ 所有服务正常运行
2. ✅ R2R API响应正常 (http://localhost:7272/v3/health)
3. ✅ Dashboard可访问 (http://localhost:7273)
4. ✅ 日志中显示SiliconFlow提供程序初始化成功
5. ✅ 搜索结果包含重排序分数

## 📞 支持

如果遇到问题：

1. 查看本指南的故障排除部分
2. 检查Docker日志
3. 验证API密钥和网络连接
4. 确保所有必需文件存在

现在您可以享受高质量的嵌入和重排序功能了！🚀
