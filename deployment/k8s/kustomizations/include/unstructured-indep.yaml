---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: unstructured
spec:
  replicas: 1
  selector:
    matchLabels:
      app: unstructured
  template:
    metadata:
      labels:
        app: unstructured
    spec:
      containers:
      - name: unstructured
        image: ragtoriches/unst-prod
        envFrom:
        - configMapRef:
            name: unstructured-configmap
        ports:
        - containerPort: 7275
        livenessProbe:
          exec:
            command: ["curl", "-f", "http://localhost:7275/health"]
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 5
---
apiVersion: v1
kind: Service
metadata:
  name: unstructured
spec:
  type: NodePort
  selector:
    app: unstructured
  ports:
  - port: 7275
    targetPort: 7275
