@echo off
echo ========================================
echo R2R Database Reset Script
echo ========================================

echo.
echo WARNING: This will delete all existing data in the database!
echo This includes all documents, embeddings, and user data.
echo.
set /p confirm="Are you sure you want to continue? (y/N): "

if /i not "%confirm%"=="y" (
    echo Operation cancelled.
    pause
    exit /b 0
)

echo.
echo Stopping all services...
docker-compose down

echo.
echo Removing PostgreSQL data volume...
docker volume rm postgres_data 2>nul
if %errorlevel% equ 0 (
    echo PostgreSQL data volume removed successfully.
) else (
    echo PostgreSQL data volume was not found or already removed.
)

echo.
echo Removing MinIO data volume...
docker volume rm minio_data 2>nul
if %errorlevel% equ 0 (
    echo MinIO data volume removed successfully.
) else (
    echo MinIO data volume was not found or already removed.
)

echo.
echo Cleaning up Docker system...
docker system prune -f

echo.
echo ========================================
echo Database Reset Complete!
echo ========================================
echo.
echo You can now run deploy_simple.bat to start fresh.
echo The new deployment will create tables with the correct dimensions.
echo.
pause
