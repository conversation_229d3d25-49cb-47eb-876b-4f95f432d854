#!/bin/bash

echo "========================================"
echo "R2R SiliconFlow Docker 部署脚本"
echo "========================================"

echo
echo "检查配置文件..."
if [ ! -f "user_configs/r2r_siliconflow.toml" ]; then
    echo "错误: 配置文件 user_configs/r2r_siliconflow.toml 不存在"
    exit 1
fi

echo
echo "检查环境变量文件..."
if [ ! -f "env/r2r.env" ]; then
    echo "错误: 环境变量文件 env/r2r.env 不存在"
    exit 1
fi

echo
echo "提醒: 请确保您已经在 env/r2r.env 文件中设置了正确的 SILICONFLOW_API_KEY"
echo

echo "停止现有容器..."
docker-compose down

echo
echo "清理旧的容器和镜像..."
docker system prune -f

echo
echo "拉取最新镜像..."
docker-compose pull

echo
echo "选择部署模式:"
echo "1. 简化模式 (推荐) - 不包含Hatchet工作流"
echo "2. 完整模式 - 包含Hatchet工作流"
read -p "请选择模式 (1 或 2): " mode

if [ "$mode" = "2" ]; then
    echo "使用完整模式部署..."
    compose_file="-f compose.full.yaml"
else
    echo "使用简化模式部署..."
    compose_file="-f compose.yaml"
fi

echo "启动服务 (按顺序启动以确保依赖关系)..."
echo "1. 启动数据库服务..."
docker-compose $compose_file --profile postgres up -d postgres

echo "等待数据库启动..."
sleep 15

echo "2. 启动MinIO存储服务..."
docker-compose $compose_file --profile minio up -d minio

echo "等待MinIO启动..."
sleep 10

echo "3. 启动图聚类服务..."
docker-compose $compose_file up -d graph_clustering

echo "等待图聚类服务启动..."
sleep 10

if [ "$mode" = "2" ]; then
    echo "4. 启动Hatchet服务..."
    docker-compose $compose_file up -d hatchet-postgres hatchet-rabbitmq
    echo "等待Hatchet基础服务启动..."
    sleep 15

    echo "5. 创建Hatchet数据库..."
    docker-compose $compose_file up hatchet-create-db

    echo "6. 运行Hatchet迁移..."
    docker-compose $compose_file up hatchet-migration

    echo "7. 设置Hatchet配置..."
    docker-compose $compose_file up hatchet-setup-config

    echo "8. 启动Hatchet引擎和Dashboard..."
    docker-compose $compose_file up -d hatchet-engine hatchet-dashboard

    echo "9. 设置API令牌..."
    docker-compose $compose_file up setup-token

    echo "等待Hatchet服务完全启动..."
    sleep 20
fi

echo "4. 启动R2R核心服务..."
docker-compose $compose_file up -d r2r

echo "等待R2R服务启动..."
sleep 15

echo "5. 启动R2R Dashboard..."
docker-compose $compose_file up -d r2r-dashboard

echo
echo "========================================"
echo "部署完成！"
echo "========================================"
echo
echo "服务地址:"
echo "- R2R API: http://localhost:7272"
echo "- R2R Dashboard: http://localhost:7273"
echo "- PostgreSQL: localhost:5432"
echo "- MinIO: http://localhost:9001"
echo
echo "默认登录信息:"
echo "- 邮箱: <EMAIL>"
echo "- 密码: change_me_immediately"
echo
echo "检查服务状态..."
docker-compose ps

echo
echo "如果遇到问题，请检查日志:"
echo "docker-compose logs r2r"
echo "docker-compose logs r2r-dashboard"
echo
