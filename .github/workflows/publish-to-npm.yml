name: Publish NPM Package

on:
  workflow_dispatch:

jobs:
  publish:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: js/sdk
    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          registry-url: 'https://registry.npmjs.org'

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 6.0.2

      - name: Install dependencies
        run: pnpm install

      - name: Build
        run: pnpm run build

      - name: Publish to npm
        run: pnpm publish --no-git-checks
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
