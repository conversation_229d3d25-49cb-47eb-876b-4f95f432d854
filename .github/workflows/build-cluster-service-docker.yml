name: Build and Publish Cluster Service Docker Image

on:
  workflow_dispatch:

env:
  R<PERSON><PERSON>TRY_BASE: ragtoriches

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install toml package
        run: pip install toml

      - name: Determine version
        id: version
        run: |
          echo "REGISTRY_IMAGE=${{ env.REGISTRY_BASE }}/cluster-prod" >> $GITHUB_OUTPUT

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: <PERSON><PERSON> Auth
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.RAGTORICHES_DOCKER_UNAME }}
          password: ${{ secrets.RAGTORICHES_DOCKER_TOKEN }}

      - name: Build and push image
        uses: docker/build-push-action@v5
        with:
          context: ./services/clustering
          file: ./services/clustering/Dockerfile.clustering
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.version.outputs.REGISTRY_IMAGE }}:latest
          provenance: false
          sbom: false

      - name: Verify manifest
        run: |
          docker buildx imagetools inspect ${{ steps.version.outputs.REGISTRY_IMAGE }}:latest
