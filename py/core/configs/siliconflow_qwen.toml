# SiliconFlow Qwen模型服务专用配置
# API Key: sk-hconcoielayrcqyfoqibziztdnchplrhkmupsvddcfkwehrd
# API Base: https://api.siliconflow.cn/v1/

[app]
# 应用基础配置
default_max_documents_per_user = 10_000
default_max_chunks_per_user = 10_000_000

# 所有LLM模型使用SiliconFlow的Qwen3-32B
fast_llm = "Qwen/Qwen3-32B"
quality_llm = "Qwen/Qwen3-32B"
vlm = "Qwen/Qwen3-32B"
reasoning_llm = "Qwen/Qwen3-32B"
planning_llm = "Qwen/Qwen3-32B"

# 音频转录仍使用OpenAI Whisper
audio_lm = "openai/whisper-1"

[completion]
provider = "litellm"
concurrent_request_limit = 64
request_timeout = 120  # SiliconFlow可能需要更长的超时时间

  [completion.generation_config]
  temperature = 0.1
  top_p = 1
  max_tokens_to_sample = 4_096
  stream = false
  api_base = "https://api.siliconflow.cn/v1/"
  add_generation_kwargs = { }

[embedding]
provider = "litellm"
base_model = "Qwen/Qwen3-Embedding-8B"
base_dimension = 1024  # Qwen3-Embedding-8B的向量维度
rerank_model = "Qwen/Qwen3-Reranker-8B"
batch_size = 64  # 适当降低批次大小以适应API限制
concurrent_request_limit = 128
initial_backoff = 1.0
quantization_settings = { quantization_type = "FP32" }

[completion_embedding]
provider = "litellm"
base_model = "Qwen/Qwen3-Embedding-8B"
base_dimension = 1024
batch_size = 64
concurrent_request_limit = 128

[ingestion]
provider = "unstructured_local"
strategy = "auto"
chunking_strategy = "by_title"
new_after_n_chars = 2_048
max_characters = 4_096
combine_under_n_chars = 1_024
overlap = 1_024

    [ingestion.extra_parsers]
    pdf = ["zerox", "ocr"]

[orchestration]
provider = "hatchet"
kg_creation_concurrency_limit = 16  # 降低并发以适应API限制
ingestion_concurrency_limit = 8
kg_concurrency_limit = 4

[agent]
rag_tools = ["search_file_descriptions", "search_file_knowledge", "get_file_content"]
research_tools = ["rag", "reasoning", "critique", "python_executor"]
