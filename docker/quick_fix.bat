@echo off
echo ========================================
echo R2R Quick Fix for Reranking Error
echo ========================================

echo.
echo This script will fix the LiteLLM reranking error by using the simple configuration.
echo.

echo Updating configuration to use simple config (no reranking)...
powershell -Command "(Get-Content env\r2r.env) -replace 'R2R_CONFIG_PATH=.*', 'R2R_CONFIG_PATH=/app/user_configs/r2r_siliconflow_simple.toml' | Set-Content env\r2r.env"

echo.
echo Configuration updated successfully!
echo.
echo Next steps:
echo 1. Edit env\r2r.env and set your SILICONFLOW_API_KEY
echo 2. Run: docker-compose down
echo 3. Run: deploy_siliconflow.bat
echo.
echo The simple configuration will work without reranking issues.
echo.
pause
