@echo off
echo ========================================
echo R2R SiliconFlow Docker 部署脚本
echo ========================================

echo.
echo 检查配置文件...
if not exist "user_configs\r2r_siliconflow.toml" (
    echo 错误: 配置文件 user_configs\r2r_siliconflow.toml 不存在
    pause
    exit /b 1
)

echo.
echo 检查环境变量文件...
if not exist "env\r2r.env" (
    echo 错误: 环境变量文件 env\r2r.env 不存在
    pause
    exit /b 1
)

echo.
echo 提醒: 请确保您已经在 env\r2r.env 文件中设置了正确的 SILICONFLOW_API_KEY
echo.

echo 停止现有容器...
docker-compose down

echo.
echo 清理旧的容器和镜像...
docker system prune -f

echo.
echo 拉取最新镜像...
docker-compose pull

echo.
echo 启动服务 (按顺序启动以确保依赖关系)...
echo 1. 启动数据库服务...
docker-compose --profile postgres up -d postgres

echo 等待数据库启动...
timeout /t 10 /nobreak > nul

echo 2. 启动MinIO存储服务...
docker-compose --profile minio up -d minio

echo 等待MinIO启动...
timeout /t 5 /nobreak > nul

echo 3. 启动图聚类服务...
docker-compose up -d graph_clustering

echo 等待图聚类服务启动...
timeout /t 5 /nobreak > nul

echo 4. 启动R2R核心服务...
docker-compose up -d r2r

echo 等待R2R服务启动...
timeout /t 10 /nobreak > nul

echo 5. 启动R2R Dashboard...
docker-compose up -d r2r-dashboard

echo.
echo ========================================
echo 部署完成！
echo ========================================
echo.
echo 服务地址:
echo - R2R API: http://localhost:7272
echo - R2R Dashboard: http://localhost:7273
echo - PostgreSQL: localhost:5432
echo - MinIO: http://localhost:9001
echo.
echo 默认登录信息:
echo - 邮箱: <EMAIL>
echo - 密码: change_me_immediately
echo.
echo 检查服务状态...
docker-compose ps

echo.
echo 如果遇到问题，请检查日志:
echo docker-compose logs r2r
echo docker-compose logs r2r-dashboard
echo.
pause
