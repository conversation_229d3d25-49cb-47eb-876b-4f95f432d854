#!/usr/bin/env python3
"""
SiliconFlow重排序代理服务
将HuggingFace TEI格式的请求转换为SiliconFlow API调用
"""

import os
import json
import logging
from typing import List, Dict, Any
from flask import Flask, request, jsonify
import requests

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# SiliconFlow配置
SILICONFLOW_API_KEY = os.getenv('SILICONFLOW_API_KEY')
SILICONFLOW_BASE_URL = "https://api.siliconflow.cn/v1"
RERANK_MODEL = "Qwen/Qwen3-Reranker-8B"

if not SILICONFLOW_API_KEY:
    logger.error("SILICONFLOW_API_KEY环境变量未设置")
    exit(1)


def call_siliconflow_rerank(query: str, documents: List[str], model: str = RERANK_MODEL) -> List[Dict[str, Any]]:
    """
    调用SiliconFlow重排序API
    """
    headers = {
        "Authorization": f"Bearer {SILICONFLOW_API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model,
        "query": query,
        "documents": documents,
        "top_k": len(documents),
        "return_documents": False
    }
    
    try:
        response = requests.post(
            f"{SILICONFLOW_BASE_URL}/rerank",
            json=payload,
            headers=headers,
            timeout=30
        )
        response.raise_for_status()
        
        result = response.json()
        return result.get("results", [])
        
    except requests.RequestException as e:
        logger.error(f"SiliconFlow API调用失败: {str(e)}")
        raise


@app.route('/rerank', methods=['POST'])
def rerank():
    """
    HuggingFace TEI兼容的重排序端点
    """
    try:
        data = request.get_json()
        
        # 提取参数
        query = data.get('query', '')
        texts = data.get('texts', [])
        model_id = data.get('model-id', RERANK_MODEL)
        
        if not query or not texts:
            return jsonify({"error": "缺少必需参数: query 和 texts"}), 400
        
        logger.info(f"处理重排序请求: query='{query[:50]}...', texts_count={len(texts)}")
        
        # 调用SiliconFlow API
        siliconflow_results = call_siliconflow_rerank(query, texts, model_id)
        
        # 转换为HuggingFace TEI格式
        hf_results = []
        for result in siliconflow_results:
            hf_results.append({
                "index": result["index"],
                "score": result["relevance_score"]
            })
        
        logger.info(f"重排序完成，返回{len(hf_results)}个结果")
        return jsonify(hf_results)
        
    except Exception as e:
        logger.error(f"重排序处理错误: {str(e)}")
        return jsonify({"error": f"重排序失败: {str(e)}"}), 500


@app.route('/health', methods=['GET'])
def health():
    """
    健康检查端点
    """
    return jsonify({
        "status": "healthy",
        "service": "SiliconFlow重排序代理",
        "model": RERANK_MODEL
    })


@app.route('/', methods=['GET'])
def info():
    """
    服务信息端点
    """
    return jsonify({
        "service": "SiliconFlow重排序代理",
        "description": "将HuggingFace TEI格式转换为SiliconFlow API调用",
        "model": RERANK_MODEL,
        "endpoints": {
            "/rerank": "POST - 重排序端点",
            "/health": "GET - 健康检查",
            "/": "GET - 服务信息"
        }
    })


if __name__ == '__main__':
    logger.info("启动SiliconFlow重排序代理服务...")
    logger.info(f"使用模型: {RERANK_MODEL}")
    
    # 在生产环境中，建议使用gunicorn等WSGI服务器
    app.run(host='0.0.0.0', port=8080, debug=False)
