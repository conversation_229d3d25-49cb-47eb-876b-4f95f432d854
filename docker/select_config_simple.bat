@echo off
echo ========================================
echo R2R SiliconFlow Configuration Selector
echo ========================================

echo.
echo Available configuration options:
echo 1. Simple Config (Recommended) - No reranking, best compatibility
echo 2. Standard Config - SiliconFlow embedding, reranking disabled
echo 3. Full Config - Local HuggingFace reranking model
echo.

set /p choice="Please select configuration (1-3): "

if "%choice%"=="1" (
    echo Selecting simple configuration...
    set config_file=r2r_siliconflow_simple.toml
    echo This config provides best compatibility without reranking
) else if "%choice%"=="2" (
    echo Selecting standard configuration...
    set config_file=r2r_siliconflow.toml
    echo This config uses SiliconFlow embedding with reranking disabled
) else if "%choice%"=="3" (
    echo Selecting full configuration...
    set config_file=r2r_siliconflow_with_rerank.toml
    echo This config includes local reranking model, requires more resources
) else (
    echo Invalid choice, using default simple configuration
    set config_file=r2r_siliconflow_simple.toml
)

echo.
echo Updating environment configuration file...

powershell -Command "(Get-Content env\r2r.env) -replace 'R2R_CONFIG_PATH=.*', 'R2R_CONFIG_PATH=/app/user_configs/%config_file%' | Set-Content env\r2r.env"

echo.
echo ========================================
echo Configuration Updated Successfully!
echo ========================================
echo.
echo Current configuration: %config_file%
echo.
echo Important reminders:
echo 1. Make sure to set your SILICONFLOW_API_KEY in env\r2r.env
echo 2. You can now run deploy_siliconflow.bat to deploy services
echo.
pause
