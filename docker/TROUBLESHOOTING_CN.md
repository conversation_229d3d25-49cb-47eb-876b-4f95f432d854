# R2R Docker 部署故障排除指南

## 常见问题及解决方案

### 1. 登录失败 - "Unable to communicate with the server"

**问题描述**: 
- 错误信息: "<PERSON><PERSON> failed. Unable to communicate with the server. Please verify the server is running at the specified URL."
- 状态码: 404
- 返回HTML页面而不是API响应

**可能原因**:
1. R2R服务未正确启动
2. 配置文件路径错误
3. API密钥未设置或错误
4. 端口冲突
5. 网络连接问题

**解决步骤**:

#### 步骤1: 检查服务状态
```bash
docker-compose ps
```
确保所有服务都在运行状态 (State: Up)

#### 步骤2: 检查R2R服务日志
```bash
docker-compose logs r2r
```
查看是否有错误信息

#### 步骤3: 检查配置文件
确保 `docker/env/r2r.env` 中设置了正确的配置路径:
```
R2R_CONFIG_PATH=/app/user_configs/r2r_siliconflow_simple.toml
```

#### 步骤4: 设置SiliconFlow API密钥
在 `docker/env/r2r.env` 文件中设置您的API密钥:
```
SILICONFLOW_API_KEY=您的实际API密钥
```

### 2. LiteLLM重排序错误

**问题**: `LiteLLMEmbeddingProvider only supports re-ranking via the HuggingFace text-embeddings-inference API`

**原因**: LiteLLM提供程序不支持通过SiliconFlow API进行重排序操作。

**解决方案**:

#### 方案1: 使用简单配置 (推荐)
运行配置选择工具：
```cmd
select_config.bat
```
选择 "1" (简单配置)，这会禁用重排序功能。

#### 方案2: 手动修改配置
编辑配置文件，移除或注释掉 `rerank_model` 配置：
```toml
[embedding]
provider = "litellm"
base_model = "Qwen/Qwen3-Embedding-8B"
# rerank_model = "Qwen/Qwen3-Reranker-8B"  # 注释掉这行
```

#### 方案3: 使用本地重排序模型
如果需要重排序功能，可以使用本地HuggingFace模型：
```toml
[rerank]
provider = "huggingface"
base_model = "BAAI/bge-reranker-base"
```

### 3. API密钥相关问题

**问题**: SiliconFlow API调用失败

**解决方案**:
1. 确保在 `docker/env/r2r.env` 中正确设置了 `SILICONFLOW_API_KEY`
2. 验证API密钥是否有效
3. 检查API配额是否充足

### 4. 配置文件问题

**问题**: 配置文件未被正确加载

**解决方案**:
1. 确保配置文件存在于 `docker/user_configs/` 目录
2. 检查文件权限
3. 验证TOML语法是否正确

### 5. 数据库连接问题

**问题**: PostgreSQL连接失败

**解决方案**:
```bash
# 检查PostgreSQL服务
docker-compose logs postgres

# 重启PostgreSQL
docker-compose restart postgres
```

### 6. Dashboard无法访问

**问题**: R2R Dashboard显示空白页面或错误

**解决方案**:
1. 检查Dashboard日志:
```bash
docker-compose logs r2r-dashboard
```

2. 确保R2R API服务正常运行
3. 检查 `docker/env/r2r-dashboard.env` 配置

### 7. Hatchet数据库创建失败

**问题**: `Container docker-hatchet-create-db-1 service "hatchet-create-db" didn't complete successfully: exit 2`

**解决方案**:

#### 方案1: 使用简化模式 (推荐)
如果您不需要Hatchet工作流功能，使用简化模式：
```bash
# 使用简化版compose文件
docker-compose -f compose.yaml up -d
```

#### 方案2: 修复Hatchet问题
如果需要Hatchet功能，运行修复脚本：

**Windows**:
```cmd
fix_hatchet.bat
```

**Linux/Mac**:
```bash
chmod +x fix_hatchet.sh
./fix_hatchet.sh
```

## 配置选择指南

### 配置类型说明

1. **简单配置** (`r2r_siliconflow_simple.toml`)
   - 不包含重排序功能
   - 兼容性最好
   - 推荐用于初次部署

2. **标准配置** (`r2r_siliconflow.toml`)
   - 包含SiliconFlow嵌入
   - 禁用重排序以避免兼容性问题

3. **完整配置** (`r2r_siliconflow_with_rerank.toml`)
   - 包含本地HuggingFace重排序模型
   - 需要更多系统资源

### 选择配置
运行配置选择工具：
```cmd
select_config.bat
```

## 完整重新部署流程

如果遇到严重问题，可以执行完整重新部署:

```bash
# 停止所有服务
docker-compose down

# 清理容器和网络
docker system prune -f

# 删除数据卷 (注意: 这会删除所有数据)
docker volume rm postgres_data minio_data

# 重新启动
docker-compose up -d
```

## 验证部署

### 1. 检查API健康状态
```bash
curl http://localhost:7272/v3/health
```

### 2. 检查服务响应
```bash
curl http://localhost:7272/v3/users
```

### 3. 访问Dashboard
打开浏览器访问: http://localhost:7273

## 日志查看命令

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs r2r
docker-compose logs r2r-dashboard
docker-compose logs postgres

# 实时查看日志
docker-compose logs -f r2r
```

## 性能优化建议

1. **内存设置**: 确保Docker有足够内存 (建议至少8GB)
2. **CPU设置**: 建议至少4核CPU
3. **磁盘空间**: 确保有足够的磁盘空间用于数据存储

## 联系支持

如果问题仍然存在，请提供以下信息:
1. 完整的错误日志
2. Docker版本信息
3. 系统配置信息
4. 使用的配置文件内容
