chunk_enrichment:
  template: >
    ## Task:

    Enrich and refine the given chunk of text while maintaining its independence and precision.

    ## Context:
    Document Summary: {document_summary}
    Preceding Chunks: {preceding_chunks}
    Succeeding Chunks: {succeeding_chunks}

    ## Input Chunk:
    {chunk}

    ## Semantic Organization Guidelines:
    1. Group related information:
       - Combine logically connected data points
       - Maintain context within each grouping
       - Preserve relationships between entities

    2. Structure hierarchy:
       - Organize from general to specific
       - Use clear categorical divisions
       - Maintain parent-child relationships

    3. Information density:
       - Balance completeness with clarity
       - Ensure each chunk can stand alone
       - Preserve essential context

    4. Pattern recognition:
       - Standardize similar information
       - Use consistent formatting for similar data types
       - It is appropriate to restructure tables or lists in ways that are more advantageous for sematic matching
       - Maintain searchable patterns

    ## Output Requirements:
    1. Each chunk should be independently meaningful
    2. Related information should stay together
    3. Format should support efficient matching
    4. Original data relationships must be preserved
    5. Context should be clear without external references

    Maximum length: {chunk_size} characters

    Output the restructured chunk only.

    ## Restructured Chunk:

  input_types:
    document_summary: str
    chunk: str
    preceding_chunks: str
    succeeding_chunks: str
    chunk_size: int
  overwrite_on_diff: true
