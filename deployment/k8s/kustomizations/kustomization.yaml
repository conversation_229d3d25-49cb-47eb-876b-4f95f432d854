# kustomize build deployment/k8s/kustomizations --enable-helm > deployment/k8s/kustomizations/r2r.kustimized.yaml

apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: ai-system

images:
#    #https://hub.docker.com/r/dpage/pgadmin4/tags
#  - name: dpage/pgadmin4
#    newTag: 8.14.0
#    #https://hub.docker.com/_/alpine/tags?name=3.2
#  - name: alpine
#    newTag: 3.21.2
    #https://hub.docker.com/_/busybox/tags?name=1.3
  - name: busybox
    newTag: 1.37.0
    #https://hub.docker.com/_/nginx/tags?name=1.27
  - name: nginx
    newTag: 1.27.3-alpine3.20-slim

    #https://github.com/SciPhi-AI/R2R-Dashboard/blob/main/Dockerfile
    #https://hub.docker.com/r/emrgntcmplxty/r2r-dashboard/tags
  - name: emrgntcmplxty/r2r-dashboard
    newTag: 1.0.0
    #https://hub.docker.com/r/ragtoriches/prod/tags?name=3.
  - name: ragtoriches/prod
    newTag: 3.4.0
    #https://hub.docker.com/r/ragtoriches/cluster-prod/tags
  - name: ragtoriches/cluster-prod
    newTag: latest
    #https://github.com/SciPhi-AI/R2R/tree/main/services/unstructured
    #https://hub.docker.com/r/ragtoriches/unst-prod/tags
  - name: ragtoriches/unst-prod
    newTag: latest

    #ghcr.io/hatchet-dev/hatchet/hatchet-dashboard
  - name: ghcr.io/hatchet-dev/hatchet/hatchet-dashboard
    newTag: v0.54.7
    #ghcr.io/hatchet-dev/hatchet/hatchet-engine
  - name: ghcr.io/hatchet-dev/hatchet/hatchet-engine
    newTag: v0.54.7
    #ghcr.io/hatchet-dev/hatchet/hatchet-admin
  - name: ghcr.io/hatchet-dev/hatchet/hatchet-admin
    newTag: v0.54.7
    #ghcr.io/hatchet-dev/hatchet/hatchet-migrate
  - name: ghcr.io/hatchet-dev/hatchet/hatchet-migrate
    newTag: v0.54.7
    #ghcr.io/hatchet-dev/hatchet/hatchet-api
  - name: ghcr.io/hatchet-dev/hatchet/hatchet-api
    newTag: v0.54.7
    #ghcr.io/hatchet-dev/hatchet/hatchet-frontend
  - name: ghcr.io/hatchet-dev/hatchet/hatchet-frontend
    newTag: v0.54.7

    #https://hub.docker.com/r/bitnami/rabbitmq/tags?name=3.
  - name: docker.io/bitnami/rabbitmq
    newTag: 3.12.14-debian-12-r7

    #https://hub.docker.com/_/postgres/tags?name=17.
  - name: postgres
    newTag: 0.8.0-pg16
    newName: pgvector/pgvector
    #https://hub.docker.com/r/pgvector/pgvector/tags?name=pg17
#  - name: pgvector/pgvector
#    newTag: 0.8.0-pg17

resources:
  - include/cm-hatchet.yaml
  - include/cm-r2r.yaml
  - include/cm-unstructured.yaml
  - include/cm-init-scripts-r2r.yaml
  - include/cm-init-scripts-hatchet.yaml

  - include/r2r-dashboard-indep.yaml
  - include/r2r-graph-clustering-indep.yaml
  - include/r2r-nginx-indep.yaml
  - include/unstructured-indep.yaml

  - include/r2r-initc.yaml
  - include/hatchet-dashboard-initc.yaml
#  - include/pgvector-sts.yaml
#  - include/pgadmin.yaml
#  - include/hatchet-init-job.yaml

helmCharts:
  - name: hatchet-ha
    #helm repo add hatchet https://hatchet-dev.github.io/hatchet-charts
    #helm repo update hatchet
    #helm search repo hatchet/hatchet-ha

    repo: https://hatchet-dev.github.io/hatchet-charts
    #version: 0.8.0
    version: 0.9.2
    releaseName: hatchet
    namespace: ai-system
    valuesFile: helm-values_hatchet.yaml
    includeCRDs: true

  - name: postgresql
    repo: oci://registry-1.docker.io/bitnamicharts
    #helm inspect chart oci://registry-1.docker.io/bitnamicharts/postgresql
    #skopeo list-tags docker://registry-1.docker.io/bitnamicharts/postgresql
    #version: 16.6.3
    version: 16.6.3
    releaseName: postgresql
    valuesFile: helm-values_postgresql.yaml
    includeCRDs: true
    # the Same Namespace
    namespace: ai-system

patches:
- path: patches/service.yaml
  target:
    kind: Service

- path: patches/hatchet-rabbitmq-sts.yaml
  target:
    kind: StatefulSet
    name: hatchet-rabbitmq

# Remove secrets generated by Helm chart
- path: patches/rm-secret-hatchet-rabbitmq-config.yaml
  target:
    kind: Secret
    name: hatchet-rabbitmq-config
- path: patches/rm-secret-hatchet-rabbitmq.yaml
  target:
    kind: Secret
    name: hatchet-rabbitmq
- path: patches/rm-secret-hatchet-shared-config.yaml
  target:
    kind: Secret
    name: hatchet-shared-config
