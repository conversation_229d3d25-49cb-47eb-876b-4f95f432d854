@echo off
echo ========================================
echo R2R SiliconFlow 自定义重排序部署
echo ========================================

echo.
echo 此脚本将部署支持Qwen3-Reranker-8B的R2R系统
echo.

echo 检查必要文件...
if not exist "siliconflow_embedding_provider.py" (
    echo 错误: 找不到 siliconflow_embedding_provider.py
    echo 请先运行 setup_custom_reranking.bat
    pause
    exit /b 1
)

if not exist "user_configs\r2r_siliconflow_with_custom_rerank.toml" (
    echo 错误: 找不到配置文件
    pause
    exit /b 1
)

echo.
echo 检查环境变量...
findstr /C:"SILICONFLOW_API_KEY=YOUR_SILICONFLOW_API_KEY_HERE" env\r2r.env >nul
if %errorlevel% equ 0 (
    echo.
    echo ⚠️  警告: 检测到默认的API密钥占位符
    echo 请编辑 env\r2r.env 文件，设置您的真实 SILICONFLOW_API_KEY
    echo.
    set /p continue="是否继续部署? (y/N): "
    if /i not "%continue%"=="y" (
        echo 部署已取消
        pause
        exit /b 0
    )
)

echo.
echo 停止现有服务...
docker-compose down

echo.
echo 清理旧镜像...
docker rmi r2r-custom:latest 2>nul

echo.
echo 构建自定义R2R镜像 (包含SiliconFlow重排序支持)...
echo 这可能需要几分钟时间...

echo # 创建临时Dockerfile > Dockerfile.temp
echo FROM r2r:latest >> Dockerfile.temp
echo. >> Dockerfile.temp
echo # 切换到root用户进行文件操作 >> Dockerfile.temp
echo USER root >> Dockerfile.temp
echo. >> Dockerfile.temp
echo # 添加自定义SiliconFlow提供程序 >> Dockerfile.temp
echo COPY siliconflow_embedding_provider.py /app/core/providers/embeddings/siliconflow.py >> Dockerfile.temp
echo. >> Dockerfile.temp
echo # 更新提供程序__init__.py >> Dockerfile.temp
echo RUN echo "" ^>^> /app/core/providers/embeddings/__init__.py >> Dockerfile.temp
echo RUN echo "from .siliconflow import SiliconFlowEmbeddingProvider" ^>^> /app/core/providers/embeddings/__init__.py >> Dockerfile.temp
echo RUN sed -i '/^__all__ = \[/,/\]/{s/\]/    "SiliconFlowEmbeddingProvider",\n]/}' /app/core/providers/embeddings/__init__.py >> Dockerfile.temp
echo. >> Dockerfile.temp
echo # 更新基础配置 >> Dockerfile.temp
echo RUN sed -i 's/return \["litellm", "openai", "ollama"\]/return ["litellm", "openai", "ollama", "siliconflow"]/' /app/core/base/providers/embedding.py >> Dockerfile.temp
echo. >> Dockerfile.temp
echo # 更新工厂类 >> Dockerfile.temp
echo RUN sed -i '/elif embedding\.provider == "litellm":/a\\n        elif embedding.provider == "siliconflow":\n            from core.providers import SiliconFlowEmbeddingProvider\n\n            embedding_provider = SiliconFlowEmbeddingProvider(embedding)' /app/core/main/assembly/factory.py >> Dockerfile.temp
echo. >> Dockerfile.temp
echo # 更新类型注解 >> Dockerfile.temp
echo RUN sed -i 's/OpenAIEmbeddingProvider$/OpenAIEmbeddingProvider | SiliconFlowEmbeddingProvider/' /app/core/main/assembly/factory.py >> Dockerfile.temp
echo. >> Dockerfile.temp
echo # 切换回app用户 >> Dockerfile.temp
echo USER app >> Dockerfile.temp

docker build -f Dockerfile.temp -t r2r-custom:latest .

if %errorlevel% neq 0 (
    echo 错误: 镜像构建失败
    del Dockerfile.temp
    pause
    exit /b 1
)

echo ✅ 自定义镜像构建完成

echo.
echo 清理临时文件...
del Dockerfile.temp

echo.
echo 更新配置文件路径...
powershell -Command "(Get-Content env\r2r.env) -replace 'R2R_CONFIG_PATH=.*', 'R2R_CONFIG_PATH=/app/user_configs/r2r_siliconflow_with_custom_rerank.toml' | Set-Content env\r2r.env"

echo.
echo 创建临时docker-compose配置...
powershell -Command "(Get-Content docker-compose.yml) -replace 'image: r2r:latest', 'image: r2r-custom:latest' | Set-Content docker-compose.temp.yml"

echo.
echo 按顺序启动服务...
echo 1. 启动数据库服务...
docker-compose -f docker-compose.temp.yml --profile postgres up -d postgres

echo 等待数据库启动...
timeout /t 15 /nobreak > nul

echo 2. 启动MinIO存储服务...
docker-compose -f docker-compose.temp.yml --profile minio up -d minio

echo 等待MinIO启动...
timeout /t 10 /nobreak > nul

echo 3. 启动图聚类服务...
docker-compose -f docker-compose.temp.yml up -d graph_clustering

echo 等待图聚类服务启动...
timeout /t 10 /nobreak > nul

echo 4. 启动R2R核心服务 (带自定义重排序)...
docker-compose -f docker-compose.temp.yml up -d r2r

echo 等待R2R服务启动...
timeout /t 20 /nobreak > nul

echo 5. 启动R2R Dashboard...
docker-compose -f docker-compose.temp.yml up -d r2r-dashboard

echo.
echo 清理临时配置文件...
del docker-compose.temp.yml

echo.
echo ========================================
echo 🎉 自定义重排序部署完成！
echo ========================================
echo.
echo 🚀 服务地址:
echo - R2R API: http://localhost:7272
echo - R2R Dashboard: http://localhost:7273
echo - PostgreSQL: localhost:5432
echo - MinIO: http://localhost:9001
echo.
echo ✨ 启用的功能:
echo - Qwen3-Embedding-8B (1024维度高质量嵌入)
echo - Qwen3-Reranker-8B (SiliconFlow自定义重排序)
echo - 完整的RAG检索和重排序流水线
echo.
echo 📋 默认登录信息:
echo - 邮箱: <EMAIL>
echo - 密码: change_me_immediately
echo.
echo 检查服务状态...
docker-compose ps

echo.
echo 🔍 如果遇到问题，请检查日志:
echo docker-compose logs r2r
echo docker-compose logs r2r-dashboard
echo.
echo 🎯 现在您可以享受高质量的嵌入和重排序功能了！
echo.
pause
