# R2R Docker 部署故障排除指南

## 常见问题及解决方案

### 1. 登录失败 - "Unable to communicate with the server"

**问题描述**: 
- 错误信息: "<PERSON><PERSON> failed. Unable to communicate with the server. Please verify the server is running at the specified URL."
- 状态码: 404
- 返回HTML页面而不是API响应

**可能原因**:
1. R2R服务未正确启动
2. 配置文件路径错误
3. API密钥未设置或错误
4. 端口冲突
5. 网络连接问题

**解决步骤**:

#### 步骤1: 检查服务状态
```bash
docker-compose ps
```
确保所有服务都在运行状态 (State: Up)

#### 步骤2: 检查R2R服务日志
```bash
docker-compose logs r2r
```
查看是否有错误信息

#### 步骤3: 检查配置文件
确保 `docker/env/r2r.env` 中设置了正确的配置路径:
```
R2R_CONFIG_PATH=/app/user_configs/r2r_siliconflow.toml
```

#### 步骤4: 设置SiliconFlow API密钥
在 `docker/env/r2r.env` 文件中设置您的API密钥:
```
SILICONFLOW_API_KEY=您的实际API密钥
```

#### 步骤5: 检查端口占用
```bash
netstat -an | findstr :7272
netstat -an | findstr :7273
```

#### 步骤6: 重启服务
```bash
docker-compose down
docker-compose up -d
```

### 2. API密钥相关问题

**问题**: SiliconFlow API调用失败

**解决方案**:
1. 确保在 `docker/env/r2r.env` 中正确设置了 `SILICONFLOW_API_KEY`
2. 验证API密钥是否有效
3. 检查API配额是否充足

### 3. 配置文件问题

**问题**: 配置文件未被正确加载

**解决方案**:
1. 确保 `docker/user_configs/r2r_siliconflow.toml` 文件存在
2. 检查文件权限
3. 验证TOML语法是否正确

### 4. 数据库连接问题

**问题**: PostgreSQL连接失败

**解决方案**:
```bash
# 检查PostgreSQL服务
docker-compose logs postgres

# 重启PostgreSQL
docker-compose restart postgres
```

### 5. Dashboard无法访问

**问题**: R2R Dashboard显示空白页面或错误

**解决方案**:
1. 检查Dashboard日志:
```bash
docker-compose logs r2r-dashboard
```

2. 确保R2R API服务正常运行
3. 检查 `docker/env/r2r-dashboard.env` 配置

## 完整重新部署流程

如果遇到严重问题，可以执行完整重新部署:

```bash
# 停止所有服务
docker-compose down

# 清理容器和网络
docker system prune -f

# 删除数据卷 (注意: 这会删除所有数据)
docker volume rm postgres_data minio_data

# 重新启动
docker-compose up -d
```

## 验证部署

### 1. 检查API健康状态
```bash
curl http://localhost:7272/v3/health
```

### 2. 检查服务响应
```bash
curl http://localhost:7272/v3/users
```

### 3. 访问Dashboard
打开浏览器访问: http://localhost:7273

## 日志查看命令

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs r2r
docker-compose logs r2r-dashboard
docker-compose logs postgres

# 实时查看日志
docker-compose logs -f r2r
```

## 性能优化建议

1. **内存设置**: 确保Docker有足够内存 (建议至少8GB)
2. **CPU设置**: 建议至少4核CPU
3. **磁盘空间**: 确保有足够的磁盘空间用于数据存储

## 联系支持

如果问题仍然存在，请提供以下信息:
1. 完整的错误日志
2. Docker版本信息
3. 系统配置信息
4. 使用的配置文件内容
