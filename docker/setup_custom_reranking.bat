@echo off
echo ========================================
echo SiliconFlow 自定义重排序设置工具
echo ========================================

echo.
echo 此工具将为您设置支持Qwen3-Reranker-8B的自定义SiliconFlow提供程序
echo.

echo 步骤1: 检查必要文件...
if not exist "siliconflow_embedding_provider.py" (
    echo 错误: 找不到 siliconflow_embedding_provider.py
    echo 请确保文件存在于当前目录
    pause
    exit /b 1
)

if not exist "user_configs\r2r_siliconflow_with_custom_rerank.toml" (
    echo 错误: 找不到配置文件 user_configs\r2r_siliconflow_with_custom_rerank.toml
    pause
    exit /b 1
)

echo ✅ 必要文件检查完成

echo.
echo 步骤2: 创建自定义Dockerfile...

echo # 基于原始R2R镜像的自定义版本 > Dockerfile.custom
echo FROM r2r:latest >> Dockerfile.custom
echo. >> Dockerfile.custom
echo # 添加自定义SiliconFlow提供程序 >> Dockerfile.custom
echo COPY siliconflow_embedding_provider.py /app/core/providers/embeddings/siliconflow.py >> Dockerfile.custom
echo. >> Dockerfile.custom
echo # 更新提供程序注册 >> Dockerfile.custom
echo RUN echo "from .siliconflow import SiliconFlowEmbeddingProvider" ^>^> /app/core/providers/embeddings/__init__.py >> Dockerfile.custom
echo RUN sed -i 's/"OllamaEmbeddingProvider",/"OllamaEmbeddingProvider",\n    "SiliconFlowEmbeddingProvider",/' /app/core/providers/embeddings/__init__.py >> Dockerfile.custom
echo. >> Dockerfile.custom
echo # 更新基础配置以支持siliconflow提供程序 >> Dockerfile.custom
echo RUN sed -i 's/return \["litellm", "openai", "ollama"\]/return ["litellm", "openai", "ollama", "siliconflow"]/' /app/core/base/providers/embedding.py >> Dockerfile.custom
echo. >> Dockerfile.custom
echo # 更新工厂类以支持siliconflow提供程序 >> Dockerfile.custom
echo RUN sed -i '/elif embedding.provider == "litellm":/a\\n        elif embedding.provider == "siliconflow":\n            from core.providers import SiliconFlowEmbeddingProvider\n\n            embedding_provider = SiliconFlowEmbeddingProvider(embedding)' /app/core/main/assembly/factory.py >> Dockerfile.custom

echo ✅ 自定义Dockerfile创建完成

echo.
echo 步骤3: 更新docker-compose.yml...

echo # 备份原始docker-compose.yml
if exist "docker-compose.yml.backup" (
    echo 备份文件已存在，跳过备份
) else (
    copy docker-compose.yml docker-compose.yml.backup
    echo ✅ 已备份原始docker-compose.yml
)

echo # 创建自定义docker-compose配置
powershell -Command "(Get-Content docker-compose.yml) -replace 'image: r2r:latest', 'build:\n      context: .\n      dockerfile: Dockerfile.custom' | Set-Content docker-compose.custom.yml"

echo ✅ 自定义docker-compose配置创建完成

echo.
echo 步骤4: 更新环境配置...
powershell -Command "(Get-Content env\r2r.env) -replace 'R2R_CONFIG_PATH=.*', 'R2R_CONFIG_PATH=/app/user_configs/r2r_siliconflow_with_custom_rerank.toml' | Set-Content env\r2r.env"

echo ✅ 环境配置更新完成

echo.
echo ========================================
echo 设置完成！
echo ========================================
echo.
echo 现在您可以使用以下命令部署带有自定义重排序的R2R：
echo.
echo 1. 构建自定义镜像：
echo    docker-compose -f docker-compose.custom.yml build
echo.
echo 2. 启动服务：
echo    docker-compose -f docker-compose.custom.yml up -d
echo.
echo 或者运行一键部署脚本：
echo    deploy_with_custom_reranking.bat
echo.
echo 重要提醒：
echo - 确保在 env\r2r.env 中设置了 SILICONFLOW_API_KEY
echo - 新配置支持 Qwen3-Embedding-8B + Qwen3-Reranker-8B
echo.
pause
