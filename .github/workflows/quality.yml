name: Code Quality Checks

on:
  push:
    branches: [ '**' ]
  pull_request:

jobs:
  pre-commit:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.x'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pre-commit
          pip install mypy
          pip install types-requests types-toml types-aiofiles

      - name: Run pre-commit hooks
        run: |
          pre-commit run --all-files
