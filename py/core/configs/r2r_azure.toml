[app]
# LLM used for internal operations, like deriving conversation names
fast_llm = "azure/gpt-4.1-mini"

# LLM used for user-facing output, like RAG replies
quality_llm = "azure/gpt-4.1"

# LLM used for ingesting visual inputs
vlm = "azure/gpt-4.1"

# LLM used for transcription
audio_lm = "azure/whisper-1"

# Reasoning model, used for `research` agent
reasoning_llm = "azure/o3-mini"
# Planning model, used for `research` agent
planning_llm = "azure/o3-mini"

[embedding]
base_model = "azure/text-embedding-3-small"

[completion_embedding]
base_model = "azure/text-embedding-3-small"
