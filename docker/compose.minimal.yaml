# 最小化R2R部署配置 - 专为SiliconFlow优化
# 只包含核心组件，避免复杂的依赖

version: '3.8'

volumes:
  postgres_data:
  redis_data:

services:
  # PostgreSQL数据库 - 使用pgvector扩展
  postgres:
    image: pgvector/pgvector:pg15
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: r2r
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # R2R核心服务
  r2r:
    image: ragtoriches/r2r:main
    environment:
      # SiliconFlow API配置
      OPENAI_API_KEY: sk-hconcoielayrcqyfoqibziztdnchplrhkmupsvddcfkwehrd
      OPENAI_API_BASE: https://api.siliconflow.cn/v1/
      
      # 数据库配置
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_HOST: postgres
      POSTGRES_PORT: 5432
      POSTGRES_DBNAME: r2r
      
      # Redis配置
      REDIS_HOST: redis
      REDIS_PORT: 6379
      
      # R2R配置
      R2R_PROJECT_NAME: r2r_siliconflow
      
      # 禁用复杂组件
      R2R_ORCHESTRATION_PROVIDER: simple
    ports:
      - "7272:7272"
      - "7273:7273"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ../py/r2r:/app/r2r
      - ../py/core/configs:/app/core/configs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7272/v3/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
