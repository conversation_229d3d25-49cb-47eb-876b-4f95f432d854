#!/bin/bash

echo "========================================"
echo "Hatchet 数据库问题修复脚本"
echo "========================================"

echo
echo "停止所有服务..."
docker-compose -f compose.full.yaml down

echo
echo "清理Hatchet相关容器和卷..."
docker volume rm hatchet_postgres_data 2>/dev/null || true
docker volume rm hatchet_rabbitmq_data 2>/dev/null || true
docker volume rm hatchet_config 2>/dev/null || true
docker volume rm hatchet_certs 2>/dev/null || true
docker volume rm hatchet_api_key 2>/dev/null || true

echo
echo "清理系统..."
docker system prune -f

echo
echo "重新创建卷..."
docker volume create hatchet_postgres_data
docker volume create hatchet_rabbitmq_data
docker volume create hatchet_config
docker volume create hatchet_certs
docker volume create hatchet_api_key

echo
echo "按步骤启动Hatchet服务..."

echo "1. 启动Hatchet PostgreSQL..."
docker-compose -f compose.full.yaml up -d hatchet-postgres
echo "等待PostgreSQL启动..."
sleep 20

echo
echo "检查PostgreSQL健康状态..."
docker-compose -f compose.full.yaml exec hatchet-postgres pg_isready -U hatchet_user -d hatchet

echo
echo "2. 启动RabbitMQ..."
docker-compose -f compose.full.yaml up -d hatchet-rabbitmq
echo "等待RabbitMQ启动..."
sleep 15

echo
echo "3. 创建数据库..."
if ! docker-compose -f compose.full.yaml up hatchet-create-db; then
    echo "数据库创建失败，尝试手动创建..."
    docker-compose -f compose.full.yaml exec hatchet-postgres createdb -U hatchet_user hatchet
fi

echo
echo "4. 运行数据库迁移..."
docker-compose -f compose.full.yaml up hatchet-migration

echo
echo "5. 设置配置..."
docker-compose -f compose.full.yaml up hatchet-setup-config

echo
echo "6. 启动Hatchet引擎和Dashboard..."
docker-compose -f compose.full.yaml up -d hatchet-engine hatchet-dashboard

echo
echo "7. 设置API令牌..."
docker-compose -f compose.full.yaml up setup-token

echo
echo "========================================"
echo "Hatchet修复完成！"
echo "========================================"
echo
echo "检查服务状态..."
docker-compose -f compose.full.yaml ps

echo
echo "如果仍有问题，请查看日志:"
echo "docker-compose -f compose.full.yaml logs hatchet-create-db"
echo "docker-compose -f compose.full.yaml logs hatchet-postgres"
echo
